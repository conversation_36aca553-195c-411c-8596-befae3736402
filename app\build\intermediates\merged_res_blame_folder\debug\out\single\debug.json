[{"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.floatingfeishu.app-merged_res-32:\\mipmap-anydpi-v26_ic_launcher_round.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.floatingfeishu.app-main-34:\\mipmap-anydpi-v26\\ic_launcher_round.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.floatingfeishu.app-merged_res-32:\\layout_simple_floating_ball.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.floatingfeishu.app-main-34:\\layout\\simple_floating_ball.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.floatingfeishu.app-merged_res-32:\\layout_activity_main.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.floatingfeishu.app-main-34:\\layout\\activity_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.floatingfeishu.app-merged_res-32:\\layout_floating_ball.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.floatingfeishu.app-main-34:\\layout\\floating_ball.xml"}, {"merged": "com.example.floatingfeishu.app-merged_res-32:/layout_simple_floating_ball.xml.flat", "source": "com.example.floatingfeishu.app-main-34:/layout/simple_floating_ball.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.floatingfeishu.app-merged_res-32:\\layout_layout_floating_ball.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.floatingfeishu.app-main-34:\\layout\\layout_floating_ball.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.floatingfeishu.app-merged_res-32:\\drawable_bg_floating_ball.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.floatingfeishu.app-main-34:\\drawable\\bg_floating_ball.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.floatingfeishu.app-merged_res-32:\\drawable_ic_launcher_foreground.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.floatingfeishu.app-main-34:\\drawable\\ic_launcher_foreground.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.floatingfeishu.app-merged_res-32:\\mipmap-anydpi-v26_ic_launcher.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.floatingfeishu.app-main-34:\\mipmap-anydpi-v26\\ic_launcher.xml"}]