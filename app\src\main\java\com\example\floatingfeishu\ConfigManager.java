package com.example.floatingfeishu;

import android.content.Context;
import android.content.SharedPreferences;
import android.util.Log;


public class ConfigManager {

    private static final String TAG = "ConfigManager";
    private static final String PREF_NAME = "FloatingFeishuConfig";
    private static final String KEY_WEBHOOK_URL = "webhook_url";
    private static final String KEY_DEFAULT_MESSAGE = "default_message";
    private static final String KEY_LAST_USED = "last_used";

    // 应用机器人相关配置
    private static final String KEY_APP_ID = "app_id";
    private static final String KEY_APP_SECRET = "app_secret";
    private static final String KEY_CHAT_ID = "chat_id";
    private static final String KEY_BOT_MODE = "bot_mode"; // true: 应用机器人模式, false: webhook模式

    private static ConfigManager instance;
    private final SharedPreferences sharedPreferences;

    private ConfigManager(Context context) {
        // 直接使用普通的SharedPreferences
        this.sharedPreferences = context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE);
    }

    public static synchronized ConfigManager getInstance(Context context) {
        if (instance == null) {
            instance = new ConfigManager(context);
        }
        return instance;
    }

    public void saveWebhookUrl(String url) {
        try {
            // 直接存储原始值，不进行加密
            sharedPreferences.edit()
                    .putString(KEY_WEBHOOK_URL, url)
                    .apply();
        } catch (Exception e) {
            Log.e(TAG, "Error saving webhook URL", e);
        }
    }

    public String getWebhookUrl() {
        try {
            // 直接返回存储的值
            return sharedPreferences.getString(KEY_WEBHOOK_URL, null);
        } catch (Exception e) {
            Log.e(TAG, "Error getting webhook URL", e);
            return null;
        }
    }

    public void saveDefaultMessage(String message) {
        try {
            sharedPreferences.edit().putString(KEY_DEFAULT_MESSAGE, message).apply();
        } catch (Exception e) {
            Log.e(TAG, "Error saving webhook URL", e);
        }
    }

    public String getDefaultMessage() {
        try {
            return sharedPreferences.getString(KEY_DEFAULT_MESSAGE, "");
        } catch (Exception e) {
            Log.e(TAG, "Error getting default message", e);
            return "";
        }
    }

    public void saveLastUsedTime(long time) {
        try {
            sharedPreferences.edit().putLong(KEY_LAST_USED, time).apply();
        } catch (Exception e) {
            Log.e(TAG, "Error saving last used time", e);
        }
    }

    @SuppressWarnings("unused")
    public long getLastUsedTime() {
        try {
            return sharedPreferences.getLong(KEY_LAST_USED, 0);
        } catch (Exception e) {
            Log.e(TAG, "Error getting last used time", e);
            return 0;
        }
    }

    // 应用机器人相关方法

    /**
     * 保存应用凭证
     */
    public void saveAppCredentials(String appId, String appSecret) {
        try {
            sharedPreferences.edit()
                    .putString(KEY_APP_ID, appId)
                    .putString(KEY_APP_SECRET, appSecret)
                    .apply();
        } catch (Exception e) {
            Log.e(TAG, "Error saving app credentials", e);
        }
    }

    /**
     * 获取应用ID
     */
    public String getAppId() {
        try {
            return sharedPreferences.getString(KEY_APP_ID, null);
        } catch (Exception e) {
            Log.e(TAG, "Error getting app ID", e);
            return null;
        }
    }

    /**
     * 获取应用密钥
     */
    public String getAppSecret() {
        try {
            return sharedPreferences.getString(KEY_APP_SECRET, null);
        } catch (Exception e) {
            Log.e(TAG, "Error getting app secret", e);
            return null;
        }
    }

    /**
     * 保存群组ID
     */
    public void saveChatId(String chatId) {
        try {
            sharedPreferences.edit()
                    .putString(KEY_CHAT_ID, chatId)
                    .apply();
        } catch (Exception e) {
            Log.e(TAG, "Error saving chat ID", e);
        }
    }

    /**
     * 获取群组ID
     */
    public String getChatId() {
        try {
            return sharedPreferences.getString(KEY_CHAT_ID, null);
        } catch (Exception e) {
            Log.e(TAG, "Error getting chat ID", e);
            return null;
        }
    }

    /**
     * 设置机器人模式
     */
    public void setBotMode(boolean isBotMode) {
        try {
            sharedPreferences.edit()
                    .putBoolean(KEY_BOT_MODE, isBotMode)
                    .apply();
        } catch (Exception e) {
            Log.e(TAG, "Error setting bot mode", e);
        }
    }

    /**
     * 获取机器人模式
     */
    public boolean isBotMode() {
        try {
            return sharedPreferences.getBoolean(KEY_BOT_MODE, false);
        } catch (Exception e) {
            Log.e(TAG, "Error getting bot mode", e);
            return false;
        }
    }
}
