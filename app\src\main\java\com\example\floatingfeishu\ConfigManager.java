package com.example.floatingfeishu;

import android.content.Context;
import android.content.SharedPreferences;
import android.util.Log;


public class ConfigManager {

    private static final String TAG = "ConfigManager";
    private static final String PREF_NAME = "FloatingFeishuConfig";
    private static final String KEY_WEBHOOK_URL = "webhook_url";
    private static final String KEY_DEFAULT_MESSAGE = "default_message";
    private static final String KEY_LAST_USED = "last_used";

    private static ConfigManager instance;
    private final SharedPreferences sharedPreferences;

    private ConfigManager(Context context) {
        // 直接使用普通的SharedPreferences
        this.sharedPreferences = context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE);
    }

    public static synchronized ConfigManager getInstance(Context context) {
        if (instance == null) {
            instance = new ConfigManager(context);
        }
        return instance;
    }

    public void saveWebhookUrl(String url) {
        try {
            // 直接存储原始值，不进行加密
            sharedPreferences.edit()
                    .putString(KEY_WEBHOOK_URL, url)
                    .apply();
        } catch (Exception e) {
            Log.e(TAG, "Error saving webhook URL", e);
        }
    }

    public String getWebhookUrl() {
        try {
            // 直接返回存储的值
            return sharedPreferences.getString(KEY_WEBHOOK_URL, null);
        } catch (Exception e) {
            Log.e(TAG, "Error getting webhook URL", e);
            return null;
        }
    }

    public void saveDefaultMessage(String message) {
        try {
            sharedPreferences.edit().putString(KEY_DEFAULT_MESSAGE, message).apply();
        } catch (Exception e) {
            Log.e(TAG, "Error saving webhook URL", e);
        }
    }

    public String getDefaultMessage() {
        try {
            return sharedPreferences.getString(KEY_DEFAULT_MESSAGE, "");
        } catch (Exception e) {
            Log.e(TAG, "Error getting default message", e);
            return "";
        }
    }

    public void saveLastUsedTime(long time) {
        try {
            sharedPreferences.edit().putLong(KEY_LAST_USED, time).apply();
        } catch (Exception e) {
            Log.e(TAG, "Error saving last used time", e);
        }
    }

    @SuppressWarnings("unused")
    public long getLastUsedTime() {
        try {
            return sharedPreferences.getLong(KEY_LAST_USED, 0);
        } catch (Exception e) {
            Log.e(TAG, "Error getting last used time", e);
            return 0;
        }
    }
}
